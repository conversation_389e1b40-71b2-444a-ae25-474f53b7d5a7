[{"/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx": "1", "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx": "2", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx": "3", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx": "4", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx": "5", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx": "6", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx": "7", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx": "8", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx": "9", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx": "10", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx": "11", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx": "12", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx": "13", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx": "14", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx": "15", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts": "16", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts": "17", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx": "18", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx": "19", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts": "20", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts": "21", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts": "22", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx": "23", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx": "24", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx": "25", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx": "26", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx": "27", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx": "28", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx": "29", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx": "30", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx": "31", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx": "32", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx": "33", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx": "34", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx": "35", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx": "36", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx": "37", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx": "38", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx": "39", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx": "40", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx": "41", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx": "42", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx": "43", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx": "44", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts": "45", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts": "46", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts": "47", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx": "48", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx": "49", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx": "50", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx": "51", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx": "52", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx": "53", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts": "54", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx": "55", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx": "56", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx": "57", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx": "58", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx": "59", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx": "60", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx": "61", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx": "62", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx": "63", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx": "64", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx": "65", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx": "66", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx": "67", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx": "68", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx": "69", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts": "70", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx": "71", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx": "72", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx": "73", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx": "74", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx": "75", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts": "76", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts": "77", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx": "78", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx": "79", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx": "80", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx": "81", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx": "82", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx": "83", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx": "84", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx": "85", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx": "86", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx": "87", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx": "88", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx": "89", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx": "90", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx": "91", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx": "92", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx": "93", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx": "94", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx": "95", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx": "96", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx": "97", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx": "98", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx": "99", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx": "100", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx": "101", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts": "102", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx": "103", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts": "104", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts": "105", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx": "106", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx": "107", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx": "108", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx": "109", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx": "110", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx": "111", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx": "112", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx": "113", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx": "114", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx": "115", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx": "116", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts": "117", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx": "118", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts": "119", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx": "120", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx": "121", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx": "122", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx": "123", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx": "124", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx": "125", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx": "126", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx": "127", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx": "128", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx": "129", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx": "130", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx": "131", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx": "132", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx": "133", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx": "134", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx": "135", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx": "136", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx": "137", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts": "138", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts": "139", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts": "140", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts": "141", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx": "142", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx": "143", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx": "144", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts": "145", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts": "146", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/AuthContext.tsx": "147", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/authService.ts": "148", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProtectedRoute.tsx": "149", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminLogin.tsx": "150", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/ScholarshipManager.tsx": "151", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/MessagesManager.tsx": "152"}, {"size": 274, "mtime": *************, "results": "153", "hashOfConfig": "154"}, {"size": 2118, "mtime": 1745983514069, "results": "155", "hashOfConfig": "154"}, {"size": 5438, "mtime": 1745977740794, "results": "156", "hashOfConfig": "154"}, {"size": 10242, "mtime": 1745981563339, "results": "157", "hashOfConfig": "154"}, {"size": 1742, "mtime": 1745977713025, "results": "158", "hashOfConfig": "154"}, {"size": 7131, "mtime": 1745981562258, "results": "159", "hashOfConfig": "154"}, {"size": 10498, "mtime": 1745982258490, "results": "160", "hashOfConfig": "154"}, {"size": 3076, "mtime": 1745945973317, "results": "161", "hashOfConfig": "154"}, {"size": 553, "mtime": 1745978322072, "results": "162", "hashOfConfig": "154"}, {"size": 6250, "mtime": 1745977773426, "results": "163", "hashOfConfig": "154"}, {"size": 2446, "mtime": 1745945132621, "results": "164", "hashOfConfig": "154"}, {"size": 2645, "mtime": 1745977581979, "results": "165", "hashOfConfig": "154"}, {"size": 4167, "mtime": 1745981843303, "results": "166", "hashOfConfig": "154"}, {"size": 5713, "mtime": 1745981758114, "results": "167", "hashOfConfig": "154"}, {"size": 675, "mtime": 1745976791748, "results": "168", "hashOfConfig": "154"}, {"size": 1059, "mtime": 1745976720607, "results": "169", "hashOfConfig": "154"}, {"size": 3452, "mtime": 1745946003719, "results": "170", "hashOfConfig": "154"}, {"size": 2518, "mtime": 1745983866923, "results": "171", "hashOfConfig": "154"}, {"size": 1737, "mtime": 1745978376608, "results": "172", "hashOfConfig": "154"}, {"size": 4075, "mtime": 1745982269507, "results": "173", "hashOfConfig": "154"}, {"size": 4531, "mtime": 1745982263875, "results": "174", "hashOfConfig": "154"}, {"size": 5406, "mtime": 1745982274929, "results": "175", "hashOfConfig": "154"}, {"size": 2535, "mtime": 1745978386143, "results": "176", "hashOfConfig": "154"}, {"size": 737, "mtime": 1745944438688, "results": "177", "hashOfConfig": "154"}, {"size": 2323, "mtime": 1745982233889, "results": "178", "hashOfConfig": "154"}, {"size": 274, "mtime": *************, "results": "179", "hashOfConfig": "180"}, {"size": 4324, "mtime": 1746276088446, "results": "181", "hashOfConfig": "180"}, {"size": 7131, "mtime": 1745981562258, "results": "182", "hashOfConfig": "180"}, {"size": 10498, "mtime": 1745982258490, "results": "183", "hashOfConfig": "180"}, {"size": 10242, "mtime": 1745981563339, "results": "184", "hashOfConfig": "180"}, {"size": 1742, "mtime": 1745977713025, "results": "185", "hashOfConfig": "180"}, {"size": 8315, "mtime": 1746204095547, "results": "186", "hashOfConfig": "180"}, {"size": 3076, "mtime": 1745945973317, "results": "187", "hashOfConfig": "180"}, {"size": 2535, "mtime": 1746033992389, "results": "188", "hashOfConfig": "180"}, {"size": 1737, "mtime": 1745978376608, "results": "189", "hashOfConfig": "180"}, {"size": 6250, "mtime": 1745977773426, "results": "190", "hashOfConfig": "180"}, {"size": 553, "mtime": 1745978322072, "results": "191", "hashOfConfig": "180"}, {"size": 2446, "mtime": 1745945132621, "results": "192", "hashOfConfig": "180"}, {"size": 5154, "mtime": 1746276100698, "results": "193", "hashOfConfig": "180"}, {"size": 8097, "mtime": 1745983506688, "results": "194", "hashOfConfig": "180"}, {"size": 6024, "mtime": 1746026061709, "results": "195", "hashOfConfig": "180"}, {"size": 9559, "mtime": 1746270350218, "results": "196", "hashOfConfig": "180"}, {"size": 737, "mtime": 1745944438688, "results": "197", "hashOfConfig": "180"}, {"size": 2645, "mtime": 1745977581979, "results": "198", "hashOfConfig": "180"}, {"size": 4531, "mtime": 1745982263875, "results": "199", "hashOfConfig": "180"}, {"size": 4075, "mtime": 1745982269507, "results": "200", "hashOfConfig": "180"}, {"size": 5406, "mtime": 1745982274929, "results": "201", "hashOfConfig": "180"}, {"size": 5713, "mtime": 1745981758114, "results": "202", "hashOfConfig": "180"}, {"size": 675, "mtime": 1745976791748, "results": "203", "hashOfConfig": "180"}, {"size": 4167, "mtime": 1745981843303, "results": "204", "hashOfConfig": "180"}, {"size": 2115, "mtime": 1746029576846, "results": "205", "hashOfConfig": "180"}, {"size": 1689, "mtime": 1745982730905, "results": "206", "hashOfConfig": "180"}, {"size": 19288, "mtime": 1745983400384, "results": "207", "hashOfConfig": "180"}, {"size": 1059, "mtime": 1745976720607, "results": "208", "hashOfConfig": "180"}, {"size": 2535, "mtime": 1745978386143, "results": "209", "hashOfConfig": "180"}, {"size": 14052, "mtime": 1746274142489, "results": "210", "hashOfConfig": "180"}, {"size": 3939, "mtime": 1746017528736, "results": "211", "hashOfConfig": "180"}, {"size": 13857, "mtime": 1746282401482, "results": "212", "hashOfConfig": "180"}, {"size": 11940, "mtime": 1746252382064, "results": "213", "hashOfConfig": "180"}, {"size": 38877, "mtime": 1746252407519, "results": "214", "hashOfConfig": "180"}, {"size": 8393, "mtime": 1746249939564, "results": "215", "hashOfConfig": "180"}, {"size": 11544, "mtime": 1746272209267, "results": "216", "hashOfConfig": "180"}, {"size": 1343, "mtime": 1746033436995, "results": "217", "hashOfConfig": "180"}, {"size": 1752, "mtime": 1746274101102, "results": "218", "hashOfConfig": "180"}, {"size": 3341, "mtime": 1746199132190, "results": "219", "hashOfConfig": "180"}, {"size": 7045, "mtime": 1746199160974, "results": "220", "hashOfConfig": "180"}, {"size": 4103, "mtime": 1746200520123, "results": "221", "hashOfConfig": "180"}, {"size": 6276, "mtime": 1746249196201, "results": "222", "hashOfConfig": "180"}, {"size": 7222, "mtime": 1746249216418, "results": "223", "hashOfConfig": "180"}, {"size": 1105, "mtime": 1746201832350, "results": "224", "hashOfConfig": "180"}, {"size": 921, "mtime": 1746202207790, "results": "225", "hashOfConfig": "180"}, {"size": 11379, "mtime": 1746276323665, "results": "226", "hashOfConfig": "180"}, {"size": 1059, "mtime": 1746226321253, "results": "227", "hashOfConfig": "180"}, {"size": 7914, "mtime": 1746251582912, "results": "228", "hashOfConfig": "180"}, {"size": 4784, "mtime": 1746252717773, "results": "229", "hashOfConfig": "180"}, {"size": 1777, "mtime": 1746254015165, "results": "230", "hashOfConfig": "180"}, {"size": 9435, "mtime": 1746252638103, "results": "231", "hashOfConfig": "180"}, {"size": 5504, "mtime": 1746275135511, "results": "232", "hashOfConfig": "180"}, {"size": 620, "mtime": 1747275499301, "results": "233", "hashOfConfig": "234"}, {"size": 4347, "mtime": 1752304663764, "results": "235", "hashOfConfig": "234"}, {"size": 13828, "mtime": 1752248890943, "results": "236", "hashOfConfig": "234"}, {"size": 19195, "mtime": 1747280789154, "results": "237", "hashOfConfig": "234"}, {"size": 3587, "mtime": 1747235972243, "results": "238", "hashOfConfig": "234"}, {"size": 10653, "mtime": 1747235897205, "results": "239", "hashOfConfig": "234"}, {"size": 3076, "mtime": 1745945973317, "results": "240", "hashOfConfig": "234"}, {"size": 1974, "mtime": 1747278879917, "results": "241", "hashOfConfig": "234"}, {"size": 553, "mtime": 1745978322072, "results": "242", "hashOfConfig": "234"}, {"size": 18635, "mtime": 1747235596822, "results": "243", "hashOfConfig": "244"}, {"size": 2446, "mtime": 1745945132621, "results": "245", "hashOfConfig": "234"}, {"size": 19874, "mtime": 1752251800113, "results": "246", "hashOfConfig": "234"}, {"size": 5486, "mtime": 1752283400302, "results": "247", "hashOfConfig": "234"}, {"size": 11326, "mtime": 1752283692783, "results": "248", "hashOfConfig": "234"}, {"size": 14078, "mtime": 1752283590200, "results": "249", "hashOfConfig": "234"}, {"size": 12834, "mtime": 1752281264858, "results": "250", "hashOfConfig": "234"}, {"size": 7045, "mtime": 1746199160974, "results": "251", "hashOfConfig": "234"}, {"size": 3341, "mtime": 1746199132190, "results": "252", "hashOfConfig": "234"}, {"size": 1059, "mtime": 1746226321253, "results": "253", "hashOfConfig": "234"}, {"size": 6276, "mtime": 1746249196201, "results": "254", "hashOfConfig": "234"}, {"size": 921, "mtime": 1746202207790, "results": "255", "hashOfConfig": "234"}, {"size": 1695, "mtime": 1747186871230, "results": "256", "hashOfConfig": "244"}, {"size": 3161, "mtime": 1747232764014, "results": "257", "hashOfConfig": "244"}, {"size": 5667, "mtime": 1747280262786, "results": "258", "hashOfConfig": "234"}, {"size": 12973, "mtime": 1747224498475, "results": "259", "hashOfConfig": "234"}, {"size": 5107, "mtime": 1747280292414, "results": "260", "hashOfConfig": "234"}, {"size": 6747, "mtime": 1747280339749, "results": "261", "hashOfConfig": "234"}, {"size": 5713, "mtime": 1745981758114, "results": "262", "hashOfConfig": "234"}, {"size": 675, "mtime": 1745976791748, "results": "263", "hashOfConfig": "244"}, {"size": 7860, "mtime": 1752251788545, "results": "264", "hashOfConfig": "234"}, {"size": 7222, "mtime": 1746249216418, "results": "265", "hashOfConfig": "234"}, {"size": 9917, "mtime": 1752283723085, "results": "266", "hashOfConfig": "234"}, {"size": 3866, "mtime": 1747221404207, "results": "267", "hashOfConfig": "244"}, {"size": 2535, "mtime": 1745978386143, "results": "268", "hashOfConfig": "234"}, {"size": 3877, "mtime": 1747235933700, "results": "269", "hashOfConfig": "234"}, {"size": 1689, "mtime": 1745982730905, "results": "270", "hashOfConfig": "234"}, {"size": 3697, "mtime": 1747184461868, "results": "271", "hashOfConfig": "244"}, {"size": 959, "mtime": 1747186815101, "results": "272", "hashOfConfig": "244"}, {"size": 5906, "mtime": 1752248869494, "results": "273", "hashOfConfig": "234"}, {"size": 2847, "mtime": 1747187027857, "results": "274", "hashOfConfig": "234"}, {"size": 2604, "mtime": 1747279467729, "results": "275", "hashOfConfig": "234"}, {"size": 8119, "mtime": 1747220020952, "results": "276", "hashOfConfig": "244"}, {"size": 8243, "mtime": 1747220059414, "results": "277", "hashOfConfig": "244"}, {"size": 3071, "mtime": 1747221577347, "results": "278", "hashOfConfig": "244"}, {"size": 6125, "mtime": 1747221750779, "results": "279", "hashOfConfig": "244"}, {"size": 6017, "mtime": 1747221715802, "results": "280", "hashOfConfig": "244"}, {"size": 3890, "mtime": 1747221780672, "results": "281", "hashOfConfig": "244"}, {"size": 3377, "mtime": 1747221613654, "results": "282", "hashOfConfig": "244"}, {"size": 3156, "mtime": 1747221640258, "results": "283", "hashOfConfig": "244"}, {"size": 7752, "mtime": 1747237735157, "results": "284", "hashOfConfig": "234"}, {"size": 11148, "mtime": 1747241866444, "results": "285", "hashOfConfig": "234"}, {"size": 9820, "mtime": 1747279005156, "results": "286", "hashOfConfig": "234"}, {"size": 8196, "mtime": 1747279261336, "results": "287", "hashOfConfig": "234"}, {"size": 9024, "mtime": 1747241959417, "results": "288", "hashOfConfig": "234"}, {"size": 9620, "mtime": 1747242054549, "results": "289", "hashOfConfig": "234"}, {"size": 9567, "mtime": 1747242099457, "results": "290", "hashOfConfig": "234"}, {"size": 5262, "mtime": 1747242002891, "results": "291", "hashOfConfig": "234"}, {"size": 5327, "mtime": 1747280365842, "results": "292", "hashOfConfig": "234"}, {"size": 28904, "mtime": 1752248907873, "results": "293", "hashOfConfig": "234"}, {"size": 1536, "mtime": 1747237627552, "results": "294", "hashOfConfig": "234"}, {"size": 2783, "mtime": 1747275467037, "results": "295", "hashOfConfig": "234"}, {"size": 4730, "mtime": 1747275582856, "results": "296", "hashOfConfig": "234"}, {"size": 5097, "mtime": 1747302359006, "results": "297", "hashOfConfig": "244"}, {"size": 1008, "mtime": 1752283918060, "results": "298", "hashOfConfig": "234"}, {"size": 9653, "mtime": 1752283637791, "results": "299", "hashOfConfig": "234"}, {"size": 358, "mtime": 1752254238558, "results": "300", "hashOfConfig": "234"}, {"size": 3337, "mtime": 1752254433306, "results": "301", "hashOfConfig": "234"}, {"size": 1433, "mtime": 1752283753270, "results": "302", "hashOfConfig": "234"}, {"size": 3435, "mtime": 1752283114606, "results": "303", "hashOfConfig": "234"}, {"size": 4212, "mtime": 1752287116909, "results": "304", "hashOfConfig": "234"}, {"size": 1083, "mtime": 1752283244823, "results": "305", "hashOfConfig": "234"}, {"size": 5685, "mtime": 1752287194809, "results": "306", "hashOfConfig": "234"}, {"size": 11164, "mtime": 1752304509058, "results": "307", "hashOfConfig": "234"}, {"size": 14378, "mtime": 1752304560424, "results": "308", "hashOfConfig": "234"}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "poe9py", {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "14ofb3m", {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qkekr7", {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15319ot", {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx", ["765"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx", ["766", "767", "768"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx", ["769"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx", ["770"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx", ["771", "772"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx", ["773", "774", "775"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx", ["776"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx", ["777", "778"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx", ["779", "780", "781"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx", [], ["782"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx", ["783"], ["784"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx", [], ["785"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx", [], ["786"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx", [], ["787"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx", ["788"], ["789"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx", ["790"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx", ["791", "792"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx", ["793", "794", "795"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx", ["796", "797"], ["798"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx", ["799", "800"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx", ["801", "802", "803", "804"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx", [], ["805"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx", [], ["806"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx", ["807"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx", ["808"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx", ["809"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx", ["810"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx", ["811"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx", ["812"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx", ["813", "814"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx", ["815", "816"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts", ["817"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts", ["818"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx", ["819"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx", ["820", "821", "822"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/authService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminLogin.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/ScholarshipManager.tsx", ["823"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/MessagesManager.tsx", [], [], {"ruleId": "824", "severity": 1, "message": "825", "line": 170, "column": 7, "nodeType": "826", "messageId": "827", "endLine": 170, "endColumn": 15}, {"ruleId": "828", "severity": 1, "message": "829", "line": 51, "column": 15, "nodeType": "830", "endLine": 51, "endColumn": 74}, {"ruleId": "828", "severity": 1, "message": "829", "line": 57, "column": 15, "nodeType": "830", "endLine": 57, "endColumn": 74}, {"ruleId": "828", "severity": 1, "message": "829", "line": 63, "column": 15, "nodeType": "830", "endLine": 63, "endColumn": 74}, {"ruleId": "824", "severity": 1, "message": "825", "line": 170, "column": 7, "nodeType": "826", "messageId": "827", "endLine": 170, "endColumn": 15}, {"ruleId": "831", "severity": 1, "message": "832", "line": 44, "column": 6, "nodeType": "833", "endLine": 44, "endColumn": 68, "suggestions": "834"}, {"ruleId": "824", "severity": 1, "message": "835", "line": 3, "column": 8, "nodeType": "826", "messageId": "827", "endLine": 3, "endColumn": 19}, {"ruleId": "824", "severity": 1, "message": "836", "line": 31, "column": 11, "nodeType": "826", "messageId": "827", "endLine": 31, "endColumn": 23}, {"ruleId": "828", "severity": 1, "message": "829", "line": 51, "column": 15, "nodeType": "830", "endLine": 51, "endColumn": 74}, {"ruleId": "828", "severity": 1, "message": "829", "line": 57, "column": 15, "nodeType": "830", "endLine": 57, "endColumn": 74}, {"ruleId": "828", "severity": 1, "message": "829", "line": 63, "column": 15, "nodeType": "830", "endLine": 63, "endColumn": 74}, {"ruleId": "824", "severity": 1, "message": "836", "line": 34, "column": 11, "nodeType": "826", "messageId": "827", "endLine": 34, "endColumn": 23}, {"ruleId": "824", "severity": 1, "message": "837", "line": 10, "column": 3, "nodeType": "826", "messageId": "827", "endLine": 10, "endColumn": 8}, {"ruleId": "824", "severity": 1, "message": "838", "line": 15, "column": 24, "nodeType": "826", "messageId": "827", "endLine": 15, "endColumn": 43}, {"ruleId": "824", "severity": 1, "message": "839", "line": 3, "column": 10, "nodeType": "826", "messageId": "827", "endLine": 3, "endColumn": 21}, {"ruleId": "824", "severity": 1, "message": "840", "line": 44, "column": 9, "nodeType": "826", "messageId": "827", "endLine": 44, "endColumn": 17}, {"ruleId": "841", "severity": 1, "message": "842", "line": 162, "column": 36, "nodeType": "843", "messageId": "844", "endLine": 162, "endColumn": 60}, {"ruleId": "831", "severity": 1, "message": "845", "line": 37, "column": 6, "nodeType": "833", "endLine": 37, "endColumn": 42, "suggestions": "846", "suppressions": "847"}, {"ruleId": "824", "severity": 1, "message": "848", "line": 236, "column": 9, "nodeType": "826", "messageId": "827", "endLine": 236, "endColumn": 30}, {"ruleId": "831", "severity": 1, "message": "845", "line": 64, "column": 6, "nodeType": "833", "endLine": 64, "endColumn": 61, "suggestions": "849", "suppressions": "850"}, {"ruleId": "831", "severity": 1, "message": "845", "line": 29, "column": 6, "nodeType": "833", "endLine": 29, "endColumn": 31, "suggestions": "851", "suppressions": "852"}, {"ruleId": "831", "severity": 1, "message": "853", "line": 25, "column": 6, "nodeType": "833", "endLine": 25, "endColumn": 8, "suggestions": "854", "suppressions": "855"}, {"ruleId": "831", "severity": 1, "message": "856", "line": 177, "column": 6, "nodeType": "833", "endLine": 177, "endColumn": 8, "suggestions": "857", "suppressions": "858"}, {"ruleId": "824", "severity": 1, "message": "839", "line": 3, "column": 10, "nodeType": "826", "messageId": "827", "endLine": 3, "endColumn": 21}, {"ruleId": "831", "severity": 1, "message": "859", "line": 37, "column": 6, "nodeType": "833", "endLine": 37, "endColumn": 8, "suggestions": "860", "suppressions": "861"}, {"ruleId": "831", "severity": 1, "message": "832", "line": 44, "column": 6, "nodeType": "833", "endLine": 44, "endColumn": 68, "suggestions": "862"}, {"ruleId": "828", "severity": 1, "message": "829", "line": 135, "column": 21, "nodeType": "830", "endLine": 135, "endColumn": 96}, {"ruleId": "828", "severity": 1, "message": "829", "line": 141, "column": 21, "nodeType": "830", "endLine": 141, "endColumn": 96}, {"ruleId": "828", "severity": 1, "message": "829", "line": 108, "column": 21, "nodeType": "830", "endLine": 108, "endColumn": 94}, {"ruleId": "828", "severity": 1, "message": "829", "line": 114, "column": 21, "nodeType": "830", "endLine": 114, "endColumn": 94}, {"ruleId": "828", "severity": 1, "message": "829", "line": 120, "column": 21, "nodeType": "830", "endLine": 120, "endColumn": 94}, {"ruleId": "824", "severity": 1, "message": "863", "line": 2, "column": 105, "nodeType": "826", "messageId": "827", "endLine": 2, "endColumn": 111}, {"ruleId": "824", "severity": 1, "message": "864", "line": 3, "column": 111, "nodeType": "826", "messageId": "827", "endLine": 3, "endColumn": 126}, {"ruleId": "831", "severity": 1, "message": "845", "line": 45, "column": 6, "nodeType": "833", "endLine": 45, "endColumn": 31, "suggestions": "865", "suppressions": "866"}, {"ruleId": "824", "severity": 1, "message": "840", "line": 44, "column": 9, "nodeType": "826", "messageId": "827", "endLine": 44, "endColumn": 17}, {"ruleId": "831", "severity": 1, "message": "867", "line": 106, "column": 6, "nodeType": "833", "endLine": 106, "endColumn": 8, "suggestions": "868"}, {"ruleId": "828", "severity": 1, "message": "829", "line": 117, "column": 17, "nodeType": "830", "endLine": 117, "endColumn": 131}, {"ruleId": "828", "severity": 1, "message": "829", "line": 125, "column": 17, "nodeType": "830", "endLine": 125, "endColumn": 131}, {"ruleId": "828", "severity": 1, "message": "829", "line": 133, "column": 17, "nodeType": "830", "endLine": 133, "endColumn": 131}, {"ruleId": "828", "severity": 1, "message": "829", "line": 141, "column": 17, "nodeType": "830", "endLine": 141, "endColumn": 131}, {"ruleId": "831", "severity": 1, "message": "859", "line": 37, "column": 6, "nodeType": "833", "endLine": 37, "endColumn": 8, "suggestions": "869", "suppressions": "870"}, {"ruleId": "831", "severity": 1, "message": "853", "line": 25, "column": 6, "nodeType": "833", "endLine": 25, "endColumn": 8, "suggestions": "871", "suppressions": "872"}, {"ruleId": "824", "severity": 1, "message": "873", "line": 38, "column": 9, "nodeType": "826", "messageId": "827", "endLine": 38, "endColumn": 12}, {"ruleId": "824", "severity": 1, "message": "836", "line": 26, "column": 11, "nodeType": "826", "messageId": "827", "endLine": 26, "endColumn": 23}, {"ruleId": "828", "severity": 1, "message": "829", "line": 101, "column": 26, "nodeType": "830", "endLine": 101, "endColumn": 87}, {"ruleId": "824", "severity": 1, "message": "874", "line": 2, "column": 40, "nodeType": "826", "messageId": "827", "endLine": 2, "endColumn": 44}, {"ruleId": "831", "severity": 1, "message": "875", "line": 38, "column": 6, "nodeType": "833", "endLine": 38, "endColumn": 8, "suggestions": "876"}, {"ruleId": "824", "severity": 1, "message": "877", "line": 25, "column": 9, "nodeType": "826", "messageId": "827", "endLine": 25, "endColumn": 21}, {"ruleId": "828", "severity": 1, "message": "829", "line": 186, "column": 21, "nodeType": "830", "endLine": 186, "endColumn": 74}, {"ruleId": "828", "severity": 1, "message": "829", "line": 188, "column": 21, "nodeType": "830", "endLine": 188, "endColumn": 74}, {"ruleId": "824", "severity": 1, "message": "874", "line": 4, "column": 30, "nodeType": "826", "messageId": "827", "endLine": 4, "endColumn": 34}, {"ruleId": "824", "severity": 1, "message": "878", "line": 7, "column": 10, "nodeType": "826", "messageId": "827", "endLine": 7, "endColumn": 16}, {"ruleId": "879", "severity": 1, "message": "880", "line": 172, "column": 1, "nodeType": "881", "endLine": 179, "endColumn": 3}, {"ruleId": "824", "severity": 1, "message": "878", "line": 2, "column": 10, "nodeType": "826", "messageId": "827", "endLine": 2, "endColumn": 16}, {"ruleId": "824", "severity": 1, "message": "882", "line": 5, "column": 16, "nodeType": "826", "messageId": "827", "endLine": 5, "endColumn": 20}, {"ruleId": "824", "severity": 1, "message": "883", "line": 13, "column": 10, "nodeType": "826", "messageId": "827", "endLine": 13, "endColumn": 20}, {"ruleId": "824", "severity": 1, "message": "884", "line": 15, "column": 10, "nodeType": "826", "messageId": "827", "endLine": 15, "endColumn": 21}, {"ruleId": "831", "severity": 1, "message": "885", "line": 106, "column": 6, "nodeType": "833", "endLine": 106, "endColumn": 8, "suggestions": "886"}, {"ruleId": "824", "severity": 1, "message": "887", "line": 32, "column": 10, "nodeType": "826", "messageId": "827", "endLine": 32, "endColumn": 22}, "@typescript-eslint/no-unused-vars", "'features' is assigned a value but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchScholarships'. Either include it or remove the dependency array.", "ArrayExpression", ["888"], "'AdminLayout' is defined but never used.", "'translations' is assigned a value but never used.", "'Space' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'useAdminApi' is defined but never used.", "'navigate' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'id'.", "ArrowFunctionExpression", "unsafeRefs", "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["889"], ["890"], "'sendEmailNotification' is assigned a value but never used.", ["891"], ["892"], ["893"], ["894"], "React Hook useEffect has a missing dependency: 'initializeSetup'. Either include it or remove the dependency array.", ["895"], ["896"], "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["897"], ["898"], "React Hook useEffect has a missing dependency: 'fetchSettings'. Either include it or remove the dependency array.", ["899"], ["900"], ["901"], "'Select' is defined but never used.", "'FilePdfOutlined' is defined but never used.", ["902"], ["903"], "React Hook useEffect has missing dependencies: 'adminInfo' and 'stats'. Either include them or remove the dependency array.", ["904"], ["905"], ["906"], ["907"], ["908"], "'api' is assigned a value but never used.", "'Spin' is defined but never used.", "React Hook useEffect has a missing dependency: 'backgrounds.length'. Either include it or remove the dependency array.", ["909"], "'scrollToTabs' is assigned a value but never used.", "'getEnv' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'Text' is assigned a value but never used.", "'DateFormat' is defined but never used.", "'ApiResponse' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSecurityEvents'. Either include it or remove the dependency array.", ["910"], "'showAddModal' is assigned a value but never used.", {"desc": "911", "fix": "912"}, {"desc": "913", "fix": "914"}, {"kind": "915", "justification": "916"}, {"desc": "917", "fix": "918"}, {"kind": "915", "justification": "916"}, {"desc": "919", "fix": "920"}, {"kind": "915", "justification": "916"}, {"desc": "921", "fix": "922"}, {"kind": "915", "justification": "916"}, {"desc": "923", "fix": "924"}, {"kind": "915", "justification": "916"}, {"desc": "925", "fix": "926"}, {"kind": "915", "justification": "916"}, {"desc": "911", "fix": "927"}, {"desc": "919", "fix": "928"}, {"kind": "915", "justification": "916"}, {"desc": "929", "fix": "930"}, {"desc": "925", "fix": "931"}, {"kind": "915", "justification": "916"}, {"desc": "921", "fix": "932"}, {"kind": "915", "justification": "916"}, {"desc": "933", "fix": "934"}, {"desc": "935", "fix": "936"}, "Update the dependencies array to be: [pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", {"range": "937", "text": "938"}, "Update the dependencies array to be: [applyFilters, messages, searchTerm, statusFilter]", {"range": "939", "text": "940"}, "directive", "", "Update the dependencies array to be: [scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", {"range": "941", "text": "942"}, "Update the dependencies array to be: [subscribers, searchTerm, applyFilters]", {"range": "943", "text": "944"}, "Update the dependencies array to be: [initializeSetup]", {"range": "945", "text": "946"}, "Update the dependencies array to be: [fetchAnalyticsData]", {"range": "947", "text": "948"}, "Update the dependencies array to be: [fetchSettings]", {"range": "949", "text": "950"}, {"range": "951", "text": "938"}, {"range": "952", "text": "944"}, "Update the dependencies array to be: [adminInfo, stats]", {"range": "953", "text": "954"}, {"range": "955", "text": "950"}, {"range": "956", "text": "946"}, "Update the dependencies array to be: [backgrounds.length]", {"range": "957", "text": "958"}, "Update the dependencies array to be: [fetchSecurityEvents]", {"range": "959", "text": "960"}, [1225, 1287], "[pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", [1158, 1194], "[applyFilters, messages, searchTerm, statusFilter]", [1966, 2021], "[scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", [903, 928], "[subscribers, searchTerm, applyFilters]", [969, 971], "[initializeSetup]", [6083, 6085], "[fetchAnalyticsData]", [1255, 1257], "[fetchSettings]", [1228, 1290], [1914, 1939], [3435, 3437], "[adminInfo, stats]", [1201, 1203], [969, 971], [1326, 1328], "[backgrounds.length]", [3118, 3120], "[fetchSecurityEvents]"]