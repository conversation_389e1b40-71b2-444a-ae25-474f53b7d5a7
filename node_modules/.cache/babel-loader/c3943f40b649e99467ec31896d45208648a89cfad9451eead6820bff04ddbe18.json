{"ast": null, "code": "import * as React from \"react\";\nfunction FireIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15.362 5.214A8.252 8.252 0 0 1 12 21 8.25 8.25 0 0 1 6.038 7.047 8.287 8.287 0 0 0 9 9.601a8.983 8.983 0 0 1 3.361-6.867 8.21 8.21 0 0 0 3 2.48Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 18a3.75 3.75 0 0 0 .495-7.468 5.99 5.99 0 0 0-1.925 3.547 5.975 5.975 0 0 1-2.133-1.001A3.75 3.75 0 0 0 12 18Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(FireIcon);\nexport default ForwardRef;", "map": {"version": 3, "names": ["React", "FireIcon", "title", "titleId", "props", "svgRef", "createElement", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "ForwardRef", "forwardRef"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/@heroicons/react/24/outline/esm/FireIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction FireIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15.362 5.214A8.252 8.252 0 0 1 12 21 8.25 8.25 0 0 1 6.038 7.047 8.287 8.287 0 0 0 9 9.601a8.983 8.983 0 0 1 3.361-6.867 8.21 8.21 0 0 0 3 2.48Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 18a3.75 3.75 0 0 0 .495-7.468 5.99 5.99 0 0 0-1.925 3.547 5.975 5.975 0 0 1-2.133-1.001A3.75 3.75 0 0 0 12 18Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(FireIcon);\nexport default ForwardRef;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQA,CAAC;EAChBC,KAAK;EACLC,OAAO;EACP,GAAGC;AACL,CAAC,EAAEC,MAAM,EAAE;EACT,OAAO,aAAaL,KAAK,CAACM,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,4BAA4B;IACnCC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,WAAW;IACpBC,WAAW,EAAE,GAAG;IAChBC,MAAM,EAAE,cAAc;IACtB,aAAa,EAAE,MAAM;IACrB,WAAW,EAAE,MAAM;IACnBC,GAAG,EAAET,MAAM;IACX,iBAAiB,EAAEF;EACrB,CAAC,EAAEC,KAAK,CAAC,EAAEF,KAAK,GAAG,aAAaF,KAAK,CAACM,aAAa,CAAC,OAAO,EAAE;IAC3DS,EAAE,EAAEZ;EACN,CAAC,EAAED,KAAK,CAAC,GAAG,IAAI,EAAE,aAAaF,KAAK,CAACM,aAAa,CAAC,MAAM,EAAE;IACzDU,aAAa,EAAE,OAAO;IACtBC,cAAc,EAAE,OAAO;IACvBC,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAalB,KAAK,CAACM,aAAa,CAAC,MAAM,EAAE;IAC3CU,aAAa,EAAE,OAAO;IACtBC,cAAc,EAAE,OAAO;IACvBC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC;AACL;AACA,MAAMC,UAAU,GAAG,aAAcnB,KAAK,CAACoB,UAAU,CAACnB,QAAQ,CAAC;AAC3D,eAAekB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}