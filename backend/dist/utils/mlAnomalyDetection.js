"use strict";
/**
 * ML-based Anomaly Detection System
 * Implements behavioral analysis and advanced threat detection algorithms
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MLAnomalyDetectionEngine = void 0;
const database_1 = require("../config/database");
class MLAnomalyDetectionEngine {
    /**
     * Update behavioral profile based on new login data
     */
    static async updateBehavioralProfile(adminId, loginData) {
        try {
            // This would typically update a machine learning model
            // For now, we'll update statistical patterns in the database
            await (0, database_1.query)(`
        INSERT INTO admin_behavioral_patterns (
          admin_id,
          login_hour,
          login_day,
          country,
          device_type,
          browser,
          success_count,
          total_count,
          last_updated
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, 1, CURRENT_TIMESTAMP)
        ON CONFLICT (admin_id, login_hour, login_day, country, device_type, browser)
        DO UPDATE SET
          success_count = admin_behavioral_patterns.success_count + $7,
          total_count = admin_behavioral_patterns.total_count + 1,
          last_updated = CURRENT_TIMESTAMP
      `, [
                adminId,
                loginData.timestamp.getHours(),
                loginData.timestamp.getDay(),
                loginData.country,
                loginData.deviceType,
                loginData.browser,
                loginData.success ? 1 : 0
            ]);
        }
        catch (error) {
            console.error('Error updating behavioral profile:', error);
        }
    }
    /**
     * Get adaptive security threshold for admin
     */
    static async getAdaptiveThreshold(adminId) {
        try {
            const profile = await this.getBehavioralProfile(adminId);
            return profile.riskThreshold;
        }
        catch (error) {
            console.error('Error getting adaptive threshold:', error);
            return 50; // Default threshold
        }
    }
    /**
     * Analyze login attempt for anomalies using ML algorithms
     */
    static async analyzeLoginAttempt(adminId, loginData) {
        try {
            // Get behavioral profile for the admin
            const profile = await this.getBehavioralProfile(adminId);
            // Calculate individual anomaly scores using ML algorithms
            const timeAnomaly = await this.calculateTimeAnomaly(loginData.timestamp, profile);
            const locationAnomaly = await this.calculateLocationAnomaly(loginData.country, profile);
            const deviceAnomaly = await this.calculateDeviceAnomaly(loginData.deviceType, loginData.browser, profile);
            const frequencyAnomaly = await this.calculateFrequencyAnomaly(adminId, loginData.timestamp);
            const behaviorAnomaly = await this.calculateBehaviorAnomaly(adminId, loginData);
            // Calculate confidence based on data availability
            const confidence = await this.calculateConfidence(adminId);
            // Calculate weighted total score using adaptive weights
            const weights = this.getAdaptiveWeights(profile, confidence);
            const totalScore = Math.min(100, Math.round((timeAnomaly * weights.time) +
                (locationAnomaly * weights.location) +
                (deviceAnomaly * weights.device) +
                (frequencyAnomaly * weights.frequency) +
                (behaviorAnomaly * weights.behavior)));
            // Determine risk level with confidence adjustment
            let riskLevel;
            const adjustedScore = totalScore * confidence;
            if (adjustedScore >= 80)
                riskLevel = 'critical';
            else if (adjustedScore >= 60)
                riskLevel = 'high';
            else if (adjustedScore >= 40)
                riskLevel = 'medium';
            else
                riskLevel = 'low';
            return {
                totalScore,
                timeAnomaly,
                locationAnomaly,
                deviceAnomaly,
                frequencyAnomaly,
                behaviorAnomaly,
                riskLevel,
                confidence
            };
        }
        catch (error) {
            console.error('Error analyzing login attempt:', error);
            // Return high risk score on error
            return {
                totalScore: 75,
                timeAnomaly: 75,
                locationAnomaly: 75,
                deviceAnomaly: 75,
                frequencyAnomaly: 75,
                behaviorAnomaly: 75,
                riskLevel: 'high',
                confidence: 0.5
            };
        }
    }
    /**
     * Get or create behavioral profile for admin using ML analysis
     */
    static async getBehavioralProfile(adminId) {
        try {
            // Get comprehensive login history for the last 90 days
            const loginHistory = await (0, database_1.query)(`
        SELECT 
          EXTRACT(HOUR FROM timestamp) as hour,
          EXTRACT(DOW FROM timestamp) as day_of_week,
          ip,
          geolocation->>'country' as country,
          device_fingerprint,
          user_agent,
          success,
          timestamp
        FROM login_attempts 
        WHERE admin_id = $1 
          AND timestamp > NOW() - INTERVAL '90 days'
        ORDER BY timestamp DESC
        LIMIT 1000
      `, [adminId]);
            if (loginHistory.rows.length < 10) {
                // Return default profile for new users with limited data
                return this.getDefaultProfile(adminId);
            }
            const logins = loginHistory.rows;
            // Advanced pattern analysis using statistical methods
            const patterns = this.analyzePatterns(logins);
            // Calculate adaptive thresholds based on user behavior
            const riskThreshold = this.calculateAdaptiveRiskThreshold(logins);
            return {
                adminId,
                normalHours: patterns.normalHours,
                normalDays: patterns.normalDays,
                commonCountries: patterns.commonCountries,
                commonDeviceTypes: patterns.commonDeviceTypes,
                commonBrowsers: patterns.commonBrowsers,
                averageSessionDuration: patterns.averageSessionDuration,
                typicalLoginFrequency: patterns.typicalLoginFrequency,
                riskThreshold
            };
        }
        catch (error) {
            console.error('Error getting behavioral profile:', error);
            return this.getDefaultProfile(adminId);
        }
    }
    /**
     * Advanced pattern analysis using statistical methods
     */
    static analyzePatterns(logins) {
        const hourCounts = new Map();
        const dayCounts = new Map();
        const countryCounts = new Map();
        const deviceCounts = new Map();
        const browserCounts = new Map();
        let successfulLogins = 0;
        let totalSessionTime = 0;
        for (const login of logins) {
            const hour = parseInt(login.hour);
            const day = parseInt(login.day_of_week);
            const country = login.country || 'unknown';
            // Extract device type and browser from user agent
            const deviceType = this.extractDeviceType(login.user_agent);
            const browser = this.extractBrowser(login.user_agent);
            // Count occurrences with weighted scoring
            const weight = login.success ? 1.2 : 0.8; // Give more weight to successful logins
            hourCounts.set(hour, (hourCounts.get(hour) || 0) + weight);
            dayCounts.set(day, (dayCounts.get(day) || 0) + weight);
            countryCounts.set(country, (countryCounts.get(country) || 0) + weight);
            deviceCounts.set(deviceType, (deviceCounts.get(deviceType) || 0) + weight);
            browserCounts.set(browser, (browserCounts.get(browser) || 0) + weight);
            if (login.success) {
                successfulLogins++;
                totalSessionTime += 3600; // Assume 1 hour average session
            }
        }
        // Use statistical analysis to determine normal patterns
        const normalHours = this.getStatisticalNormalRange(hourCounts, 0.85);
        const normalDays = this.getStatisticalNormalRange(dayCounts, 0.8);
        const commonCountries = this.getTopPercentileKeys(countryCounts, 0.9);
        const commonDeviceTypes = this.getTopPercentileKeys(deviceCounts, 0.8);
        const commonBrowsers = this.getTopPercentileKeys(browserCounts, 0.8);
        // Calculate metrics
        const daySpan = Math.max(1, Math.floor((Date.now() - new Date(logins[logins.length - 1].timestamp).getTime()) / (1000 * 60 * 60 * 24)));
        const typicalLoginFrequency = logins.length / daySpan;
        const averageSessionDuration = successfulLogins > 0 ? totalSessionTime / successfulLogins : 3600;
        return {
            normalHours,
            normalDays,
            commonCountries,
            commonDeviceTypes,
            commonBrowsers,
            averageSessionDuration,
            typicalLoginFrequency
        };
    }
    /**
     * Calculate adaptive risk threshold based on user behavior
     */
    static calculateAdaptiveRiskThreshold(logins) {
        const successfulLogins = logins.filter(l => l.success).length;
        const totalLogins = logins.length;
        const successRate = totalLogins > 0 ? successfulLogins / totalLogins : 0.5;
        // Calculate variance in login patterns
        const hourVariance = this.calculateTimeVariance(logins);
        const locationVariance = this.calculateLocationVariance(logins);
        // Base threshold on success rate and pattern consistency
        let threshold = 50; // Default threshold
        // Adjust based on success rate
        if (successRate > 0.9)
            threshold -= 10; // Very reliable user
        else if (successRate < 0.7)
            threshold += 15; // Less reliable user
        // Adjust based on pattern consistency
        if (hourVariance < 0.3)
            threshold -= 5; // Consistent time patterns
        if (locationVariance < 0.2)
            threshold -= 5; // Consistent location patterns
        return Math.max(20, Math.min(80, threshold));
    }
    /**
     * Calculate confidence score based on data availability and quality
     */
    static async calculateConfidence(adminId) {
        try {
            const dataQuality = await (0, database_1.query)(`
        SELECT 
          COUNT(*) as total_logins,
          COUNT(DISTINCT DATE(timestamp)) as active_days,
          COUNT(DISTINCT ip) as unique_ips,
          AVG(CASE WHEN success THEN 1 ELSE 0 END) as success_rate
        FROM login_attempts 
        WHERE admin_id = $1 
          AND timestamp > NOW() - INTERVAL '90 days'
      `, [adminId]);
            const stats = dataQuality.rows[0];
            const totalLogins = parseInt(stats.total_logins);
            const activeDays = parseInt(stats.active_days);
            const uniqueIps = parseInt(stats.unique_ips);
            const successRate = parseFloat(stats.success_rate) || 0;
            let confidence = 0.5; // Base confidence
            // Adjust based on data volume
            if (totalLogins > 100)
                confidence += 0.2;
            else if (totalLogins > 50)
                confidence += 0.1;
            else if (totalLogins < 10)
                confidence -= 0.2;
            // Adjust based on activity consistency
            if (activeDays > 30)
                confidence += 0.15;
            else if (activeDays > 14)
                confidence += 0.1;
            else if (activeDays < 7)
                confidence -= 0.1;
            // Adjust based on behavior consistency
            if (successRate > 0.8)
                confidence += 0.1;
            else if (successRate < 0.5)
                confidence -= 0.1;
            // Adjust based on location consistency
            if (uniqueIps < 5)
                confidence += 0.05;
            else if (uniqueIps > 20)
                confidence -= 0.05;
            return Math.max(0.1, Math.min(1.0, confidence));
        }
        catch (error) {
            console.error('Error calculating confidence:', error);
            return 0.5;
        }
    }
    /**
     * Get adaptive weights based on profile and confidence
     */
    static getAdaptiveWeights(profile, confidence) {
        // Base weights
        let weights = {
            time: 0.2,
            location: 0.3,
            device: 0.2,
            frequency: 0.15,
            behavior: 0.15
        };
        // Adjust weights based on profile characteristics
        if (profile.commonCountries.length === 1) {
            // User always from same country - increase location weight
            weights.location += 0.1;
            weights.time -= 0.05;
            weights.device -= 0.05;
        }
        if (profile.normalHours.length <= 3) {
            // Very consistent time patterns - increase time weight
            weights.time += 0.1;
            weights.frequency -= 0.05;
            weights.behavior -= 0.05;
        }
        // Adjust based on confidence
        if (confidence < 0.5) {
            // Low confidence - reduce all weights slightly
            Object.keys(weights).forEach(key => {
                weights[key] *= 0.8;
            });
        }
        return weights;
    }
    /**
     * Helper methods for statistical analysis
     */
    static getStatisticalNormalRange(counts, percentile) {
        const sorted = Array.from(counts.entries()).sort((a, b) => b[1] - a[1]);
        const total = sorted.reduce((sum, [, count]) => sum + count, 0);
        const threshold = total * percentile;
        let cumulative = 0;
        const result = [];
        for (const [key, count] of sorted) {
            cumulative += count;
            result.push(key);
            if (cumulative >= threshold)
                break;
        }
        return result;
    }
    static getTopPercentileKeys(counts, percentile) {
        const sorted = Array.from(counts.entries()).sort((a, b) => b[1] - a[1]);
        const cutoff = Math.ceil(sorted.length * percentile);
        return sorted.slice(0, cutoff).map(([key]) => key);
    }
    static calculateTimeVariance(logins) {
        const hours = logins.map(l => parseInt(l.hour));
        const mean = hours.reduce((sum, h) => sum + h, 0) / hours.length;
        const variance = hours.reduce((sum, h) => sum + Math.pow(h - mean, 2), 0) / hours.length;
        return variance / 144; // Normalize to 0-1 range (24^2 / 4)
    }
    static calculateLocationVariance(logins) {
        const countries = logins.map(l => l.country || 'unknown');
        const uniqueCountries = new Set(countries);
        return Math.min(1, uniqueCountries.size / 10); // Normalize to 0-1 range
    }
    static extractDeviceType(userAgent) {
        const ua = userAgent.toLowerCase();
        if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone'))
            return 'mobile';
        if (ua.includes('tablet') || ua.includes('ipad'))
            return 'tablet';
        return 'desktop';
    }
    static extractBrowser(userAgent) {
        const ua = userAgent.toLowerCase();
        if (ua.includes('chrome'))
            return 'chrome';
        if (ua.includes('firefox'))
            return 'firefox';
        if (ua.includes('safari'))
            return 'safari';
        if (ua.includes('edge'))
            return 'edge';
        return 'unknown';
    }
    static getDefaultProfile(adminId) {
        return {
            adminId,
            normalHours: [8, 9, 10, 11, 12, 13, 14, 15, 16, 17], // Business hours
            normalDays: [1, 2, 3, 4, 5], // Weekdays
            commonCountries: [],
            commonDeviceTypes: ['desktop'],
            commonBrowsers: ['chrome'],
            averageSessionDuration: 3600,
            typicalLoginFrequency: 2,
            riskThreshold: 50
        };
    }
    /**
     * Calculate time-based anomaly score using ML algorithms
     */
    static async calculateTimeAnomaly(timestamp, profile) {
        const hour = timestamp.getHours();
        const dayOfWeek = timestamp.getDay();
        let score = 0;
        // Advanced time pattern analysis
        if (!profile.normalHours.includes(hour)) {
            // Calculate distance from normal hours
            const distances = profile.normalHours.map(h => Math.min(Math.abs(h - hour), 24 - Math.abs(h - hour)));
            const minDistance = Math.min(...distances);
            score += Math.min(60, minDistance * 10);
            // Extra penalty for very unusual hours (2-6 AM)
            if (hour >= 2 && hour <= 6) {
                score += 25;
            }
        }
        // Day pattern analysis
        if (!profile.normalDays.includes(dayOfWeek)) {
            score += 20;
            // Weekend login penalty for business users
            if (dayOfWeek === 0 || dayOfWeek === 6) {
                score += 15;
            }
        }
        return Math.min(100, score);
    }
    /**
     * Calculate location-based anomaly score
     */
    static async calculateLocationAnomaly(country, profile) {
        if (!country || country === 'unknown') {
            return 60; // Unknown location is moderately suspicious
        }
        if (profile.commonCountries.length === 0) {
            return 30; // New user, moderate suspicion
        }
        if (!profile.commonCountries.includes(country)) {
            return 85; // New country is highly suspicious
        }
        return 0; // Known country is normal
    }
    /**
     * Calculate device-based anomaly score
     */
    static async calculateDeviceAnomaly(deviceType, browser, profile) {
        let score = 0;
        if (!profile.commonDeviceTypes.includes(deviceType)) {
            score += 35;
        }
        if (!profile.commonBrowsers.includes(browser)) {
            score += 25;
        }
        return Math.min(100, score);
    }
    /**
     * Calculate frequency-based anomaly score
     */
    static async calculateFrequencyAnomaly(adminId, timestamp) {
        try {
            // Check recent login frequency patterns
            const recentActivity = await (0, database_1.query)(`
        SELECT 
          COUNT(*) as count_24h,
          COUNT(*) FILTER (WHERE timestamp > $2 - INTERVAL '1 hour') as count_1h,
          COUNT(*) FILTER (WHERE timestamp > $2 - INTERVAL '10 minutes') as count_10m
        FROM login_attempts 
        WHERE admin_id = $1 
          AND timestamp > $2 - INTERVAL '24 hours'
          AND timestamp <= $2
      `, [adminId, timestamp]);
            const activity = recentActivity.rows[0];
            const count24h = parseInt(activity.count_24h);
            const count1h = parseInt(activity.count_1h);
            const count10m = parseInt(activity.count_10m);
            let score = 0;
            // Very high frequency in short time windows
            if (count10m > 5)
                score += 70;
            else if (count1h > 10)
                score += 50;
            else if (count24h > 30)
                score += 30;
            return Math.min(100, score);
        }
        catch (error) {
            console.error('Error calculating frequency anomaly:', error);
            return 25;
        }
    }
    /**
     * Calculate behavioral anomaly score
     */
    static async calculateBehaviorAnomaly(adminId, loginData) {
        try {
            let score = 0;
            // Check for rapid IP changes
            const recentIPs = await (0, database_1.query)(`
        SELECT DISTINCT ip, COUNT(*) as count
        FROM login_attempts 
        WHERE admin_id = $1 
          AND timestamp > NOW() - INTERVAL '2 hours'
        GROUP BY ip
      `, [adminId]);
            if (recentIPs.rows.length > 3) {
                score += 60; // Multiple IPs in short time is highly suspicious
            }
            // User agent analysis
            const userAgentLength = loginData.userAgent.length;
            if (userAgentLength < 50 || userAgentLength > 500) {
                score += 30; // Unusual user agent length
            }
            // Check for suspicious user agent patterns
            const suspiciousPatterns = ['bot', 'crawler', 'spider', 'automated'];
            const ua = loginData.userAgent.toLowerCase();
            if (suspiciousPatterns.some(pattern => ua.includes(pattern))) {
                score += 80;
            }
            return Math.min(100, score);
        }
        catch (error) {
            console.error('Error calculating behavior anomaly:', error);
            return 25;
        }
    }
}
exports.MLAnomalyDetectionEngine = MLAnomalyDetectionEngine;
