"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const auth_new_1 = require("../middleware/auth.new");
const database_1 = require("../config/database");
const router = express_1.default.Router();
// Apply authentication middleware to all admin routes
router.use(auth_new_1.authenticate);
router.use(auth_new_1.requireAdmin);
/**
 * @route GET /api/admin/current
 * @desc Get current admin profile
 * @access Private (Admin only)
 */
router.get('/current', async (req, res) => {
    var _a;
    try {
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!adminId) {
            return res.status(401).json({ message: 'Unauthorized' });
        }
        const result = await (0, database_1.query)('SELECT id, name, email, role, "isMainAdmin", privileges FROM admins WHERE id = $1', [adminId]);
        if (result.rows.length === 0) {
            return res.status(404).json({ message: 'Admin not found' });
        }
        const admin = result.rows[0];
        // Parse privileges if stored as string
        let privileges;
        try {
            privileges = typeof admin.privileges === 'string'
                ? JSON.parse(admin.privileges)
                : admin.privileges;
        }
        catch (error) {
            privileges = admin.privileges;
        }
        res.json({
            id: admin.id,
            name: admin.name,
            email: admin.email,
            role: admin.role,
            isMainAdmin: admin.isMainAdmin,
            privileges
        });
    }
    catch (error) {
        console.error('Get current admin error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route GET /api/admin/me
 * @desc Get current admin profile (alias for /current)
 * @access Private (Admin only)
 */
router.get('/me', async (req, res) => {
    var _a;
    try {
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!adminId) {
            return res.status(401).json({ message: 'Unauthorized' });
        }
        const result = await (0, database_1.query)('SELECT id, name, email, role, "isMainAdmin", privileges FROM admins WHERE id = $1', [adminId]);
        if (result.rows.length === 0) {
            return res.status(404).json({ message: 'Admin not found' });
        }
        const admin = result.rows[0];
        // Parse privileges if stored as string
        let privileges;
        try {
            privileges = typeof admin.privileges === 'string'
                ? JSON.parse(admin.privileges)
                : admin.privileges;
        }
        catch (error) {
            privileges = admin.privileges;
        }
        res.json({
            id: admin.id,
            name: admin.name,
            email: admin.email,
            role: admin.role,
            isMainAdmin: admin.isMainAdmin,
            privileges
        });
    }
    catch (error) {
        console.error('Get current admin error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route GET /api/admin/stats
 * @desc Get dashboard statistics
 * @access Private (Admin only)
 */
router.get('/stats', async (req, res) => {
    try {
        // Get counts from all tables
        const [scholarshipsResult, messagesResult, subscribersResult, adminsResult] = await Promise.all([
            (0, database_1.query)('SELECT COUNT(*) as count FROM scholarships'),
            (0, database_1.query)('SELECT COUNT(*) as count FROM messages'),
            (0, database_1.query)('SELECT COUNT(*) as count FROM newsletter_subscriptions'),
            (0, database_1.query)('SELECT COUNT(*) as count FROM admins')
        ]);
        res.json({
            totalScholarships: parseInt(scholarshipsResult.rows[0].count),
            totalMessages: parseInt(messagesResult.rows[0].count),
            totalSubscribers: parseInt(subscribersResult.rows[0].count),
            totalAdmins: parseInt(adminsResult.rows[0].count)
        });
    }
    catch (error) {
        console.error('Get stats error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route GET /api/admin/analytics
 * @desc Get detailed analytics data
 * @access Private (Admin only)
 */
router.get('/analytics', async (req, res) => {
    try {
        // Get scholarships by status
        const statusResult = await (0, database_1.query)(`
      SELECT
        CASE WHEN is_open = true THEN 'Open' ELSE 'Closed' END as status,
        COUNT(*) as count
      FROM scholarships
      GROUP BY is_open
    `);
        const scholarshipsByStatus = statusResult.rows.map(row => ({
            name: row.status,
            value: parseInt(row.count)
        }));
        // Get scholarships by level
        const levelResult = await (0, database_1.query)(`
      SELECT level, COUNT(*) as count
      FROM scholarships
      WHERE level IS NOT NULL
      GROUP BY level
    `);
        const scholarshipsByLevel = levelResult.rows.map(row => ({
            name: row.level || 'Unspecified',
            value: parseInt(row.count)
        }));
        // Get scholarships by country
        const countryResult = await (0, database_1.query)(`
      SELECT country, COUNT(*) as count
      FROM scholarships
      WHERE country IS NOT NULL
      GROUP BY country
      ORDER BY count DESC
      LIMIT 10
    `);
        const scholarshipsByCountry = countryResult.rows.map(row => ({
            name: row.country || 'Unspecified',
            value: parseInt(row.count)
        }));
        // Get recent activity (last 30 days)
        const activityResult = await (0, database_1.query)(`
      SELECT
        DATE(created_at) as date,
        COUNT(*) as count
      FROM scholarships
      WHERE created_at >= NOW() - INTERVAL '30 days'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `);
        const recentActivity = activityResult.rows.map(row => ({
            date: row.date,
            scholarships: parseInt(row.count)
        }));
        res.json({
            scholarshipsByStatus,
            scholarshipsByLevel,
            scholarshipsByCountry,
            recentActivity
        });
    }
    catch (error) {
        console.error('Get analytics error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route GET /api/admin/security/events
 * @desc Get security events (proxy to security dashboard)
 * @access Private (Admin only)
 */
router.get('/security/events', async (req, res) => {
    try {
        // For now, return mock data since security events table might not exist
        // This can be updated when the security events system is fully implemented
        const mockEvents = [
            {
                id: 1,
                timestamp: new Date().toISOString(),
                eventType: 'login_attempt',
                severity: 'info',
                ip: '127.0.0.1',
                email: '<EMAIL>',
                description: 'Successful admin login',
                resolved: true
            }
        ];
        const mockStats = {
            totalEvents: 1,
            highRiskEvents: 0,
            resolvedEvents: 1,
            pendingEvents: 0
        };
        res.json({
            success: true,
            data: {
                events: mockEvents,
                stats: mockStats,
                pagination: {
                    page: 1,
                    limit: 10,
                    total: 1,
                    totalPages: 1
                }
            }
        });
    }
    catch (error) {
        console.error('Get security events error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
// Main admin only routes
router.use(auth_new_1.requireMainAdmin);
/**
 * @route POST /api/admin
 * @desc Create new admin (main admin only)
 * @access Private (Main Admin only)
 */
router.post('/', [
    (0, express_validator_1.body)('name').notEmpty().withMessage('Name is required'),
    (0, express_validator_1.body)('email').isEmail().withMessage('Please enter a valid email'),
    (0, express_validator_1.body)('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
    (0, express_validator_1.body)('role').isIn(['admin', 'super_admin']).withMessage('Invalid role'),
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const { name, email, password, role, isMainAdmin = false } = req.body;
        // Check if admin already exists
        const existingResult = await (0, database_1.query)('SELECT id FROM admins WHERE email = $1', [email]);
        if (existingResult.rows.length > 0) {
            return res.status(409).json({ message: 'Admin already exists with this email' });
        }
        // Hash password
        const hashedPassword = await bcryptjs_1.default.hash(password, 10);
        // Create new admin
        const privileges = role === 'super_admin' ? ['all'] : ['manage_scholarships', 'manage_messages'];
        const result = await (0, database_1.query)(`
      INSERT INTO admins (name, email, password, role, privileges, "isMainAdmin", "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
      RETURNING id, name, email, role, "isMainAdmin", privileges
    `, [
            name,
            email,
            hashedPassword,
            role,
            JSON.stringify(privileges),
            isMainAdmin && email === '<EMAIL>' // Only allow main admin for specific email
        ]);
        const admin = result.rows[0];
        res.status(201).json({
            id: admin.id,
            name: admin.name,
            email: admin.email,
            role: admin.role,
            isMainAdmin: admin.isMainAdmin,
            privileges: JSON.parse(admin.privileges)
        });
    }
    catch (error) {
        console.error('Create admin error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
/**
 * @route GET /api/admin/all
 * @desc Get all admins (main admin only)
 * @access Private (Main Admin only)
 */
router.get('/all', async (req, res) => {
    try {
        const result = await (0, database_1.query)(`
      SELECT id, name, email, role, "isMainAdmin", privileges, "createdAt"
      FROM admins
      ORDER BY "createdAt" DESC
    `);
        // Parse privileges for each admin
        const formattedAdmins = result.rows.map(admin => {
            try {
                return {
                    ...admin,
                    privileges: typeof admin.privileges === 'string'
                        ? JSON.parse(admin.privileges)
                        : admin.privileges
                };
            }
            catch (error) {
                console.error(`Error parsing privileges for admin ${admin.id}:`, error);
                return {
                    ...admin,
                    privileges: typeof admin.privileges === 'string'
                        ? [admin.privileges]
                        : admin.privileges
                };
            }
        });
        res.json(formattedAdmins);
    }
    catch (error) {
        console.error('Get all admins error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});
exports.default = router;
